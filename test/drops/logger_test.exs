defmodule Drops.LoggerTest do
  use ExUnit.Case, async: false

  alias Drops.Logger, as: DropsLogger

  setup do
    # Store original configuration
    original_config = Application.get_env(:drops, :logger, [])

    # Ensure handler is removed before each test
    DropsLogger.remove_handler()

    on_exit(fn ->
      DropsLogger.remove_handler()
      Application.put_env(:drops, :logger, original_config)
      DropsLogger.clear_logs()
    end)

    :ok
  end

  describe "init/0" do
    test "initializes handler when logger configuration is present" do
      Application.put_env(:drops, :logger,
        handler: :memory,
        level: :debug
      )

      assert :ok = DropsLogger.init()

      # Verify handler was added
      {:ok, _config} = :logger.get_handler_config(:drops_debug_handler)
    end

    test "does not initialize handler when no logger configuration" do
      Application.put_env(:drops, :logger, [])

      assert :ok = DropsLogger.init()

      # Verify handler was not added
      assert {:error, {:not_found, :drops_debug_handler}} =
               :logger.get_handler_config(:drops_debug_handler)
    end
  end

  describe "add_handler/0" do
    test "adds debug handler with console configuration" do
      Application.put_env(:drops, :logger,
        handler: :console,
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler was added
      {:ok, _config} = :logger.get_handler_config(:drops_debug_handler)
    end

    test "adds debug handler with file configuration" do
      Application.put_env(:drops, :logger,
        handler: :file,
        file: "test/tmp/test.log",
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler was added
      {:ok, _config} = :logger.get_handler_config(:drops_debug_handler)

      # Clean up test file
      File.rm_rf!("test/tmp")
    end

    test "adds debug handler with memory configuration" do
      Application.put_env(:drops, :logger,
        handler: :memory,
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler was added
      {:ok, _config} = :logger.get_handler_config(:drops_debug_handler)
    end

    test "returns ok when handler already exists" do
      Application.put_env(:drops, :logger,
        handler: :memory,
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()
      # Second call should also return :ok
      assert :ok = DropsLogger.add_handler()
    end

    test "uses default configuration when no config provided" do
      Application.put_env(:drops, :logger, [])

      assert :ok = DropsLogger.add_handler()

      # Verify handler was added with defaults
      {:ok, _config} = :logger.get_handler_config(:drops_debug_handler)
    end
  end

  describe "remove_handler/0" do
    test "removes debug handler successfully" do
      Application.put_env(:drops, :logger,
        handler: :memory,
        level: :debug
      )

      DropsLogger.add_handler()

      # Verify handler exists
      {:ok, _config} = :logger.get_handler_config(:drops_debug_handler)

      assert :ok = DropsLogger.remove_handler()

      # Verify handler was removed
      assert {:error, {:not_found, :drops_debug_handler}} =
               :logger.get_handler_config(:drops_debug_handler)
    end

    test "returns ok when handler does not exist" do
      assert :ok = DropsLogger.remove_handler()
    end
  end

  describe "memory handler operations" do
    setup do
      # Store original logger configuration
      original_logger_config = Application.get_env(:logger, :level)
      original_primary_level = :logger.get_primary_config().level

      # Set logger configuration to allow debug logs
      Application.put_env(:logger, :level, :debug)
      :logger.set_primary_config(level: :debug)

      # Also set process-level logger to debug to override any process-level filtering
      Logger.configure(level: :debug)

      Application.put_env(:drops, :logger,
        handler: :memory,
        level: :debug,
        format: "[$level] $message $metadata\n",
        metadata: [:operation, :step]
      )

      DropsLogger.remove_handler()
      DropsLogger.add_handler()
      DropsLogger.clear_logs()

      on_exit(fn ->
        # Clean up handler and restore original logger configuration
        DropsLogger.remove_handler()
        Application.put_env(:logger, :level, original_logger_config)
        :logger.set_primary_config(level: original_primary_level)
      end)

      :ok
    end

    test "get_logs/0 returns captured logs" do
      # Initially empty
      assert DropsLogger.get_logs() == []

      # Log a debug message with metadata
      require Logger, as: ElixirLogger
      ElixirLogger.debug("Test message", operation: "TestOp", step: "test_step")

      # Give logger time to process
      Process.sleep(50)

      logs = DropsLogger.get_logs()
      assert length(logs) == 1
      assert hd(logs) =~ "Test message"
    end

    test "clear_logs/0 clears captured logs" do
      # Log a message
      require Logger, as: ElixirLogger
      ElixirLogger.debug("Test message", operation: "TestOp", step: "test_step")

      # Give logger time to process
      Process.sleep(10)

      # Verify log exists
      assert length(DropsLogger.get_logs()) == 1

      # Clear logs
      assert :ok = DropsLogger.clear_logs()

      # Verify logs are cleared
      assert DropsLogger.get_logs() == []
    end

    test "logs are captured for all levels with operation metadata" do
      require Logger, as: ElixirLogger

      # Log at different levels
      ElixirLogger.debug("Debug message", operation: "TestOp", step: "test_step")
      ElixirLogger.info("Info message", operation: "TestOp", step: "test_step")
      ElixirLogger.warning("Warning message", operation: "TestOp", step: "test_step")

      # Give logger time to process
      Process.sleep(10)

      logs = DropsLogger.get_logs()
      # All operation-related messages should be captured
      assert length(logs) == 3
      assert Enum.any?(logs, &(&1 =~ "Debug message"))
      assert Enum.any?(logs, &(&1 =~ "Info message"))
      assert Enum.any?(logs, &(&1 =~ "Warning message"))
    end
  end

  describe "formatter configuration" do
    test "configures string formatter for console handler" do
      Application.put_env(:drops, :logger,
        handler: :console,
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler was configured with string formatter
      {:ok, config} = :logger.get_handler_config(:drops_debug_handler)

      assert {Drops.Logger.Formatter.String, %Drops.Logger.Formatter.Config{}} =
               config.formatter

      assert config.level == :debug
    end

    test "configures JSON formatter for console handler" do
      Application.put_env(:drops, :logger,
        handler: :console,
        formatter: :json,
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler was configured with JSON formatter
      {:ok, config} = :logger.get_handler_config(:drops_debug_handler)

      assert {Drops.Logger.Formatter.Structured, %Drops.Logger.Formatter.Config{}} =
               config.formatter

      assert config.level == :debug
    end

    test "configures string formatter for file handler" do
      Application.put_env(:drops, :logger,
        handler: :file,
        file: "test/tmp/formatter_test.log",
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler was configured with string formatter
      {:ok, config} = :logger.get_handler_config(:drops_debug_handler)

      assert {Drops.Logger.Formatter.String, %Drops.Logger.Formatter.Config{}} =
               config.formatter

      assert config.level == :debug

      # Clean up test file
      File.rm_rf!("test/tmp")
    end

    test "configures JSON formatter for file handler" do
      Application.put_env(:drops, :logger,
        handler: :file,
        file: "test/tmp/formatter_test.log",
        formatter: :json,
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler was configured with JSON formatter
      {:ok, config} = :logger.get_handler_config(:drops_debug_handler)

      assert {Drops.Logger.Formatter.Structured, %Drops.Logger.Formatter.Config{}} =
               config.formatter

      assert config.level == :debug

      # Clean up test file
      File.rm_rf!("test/tmp")
    end

    test "defaults to string formatter when not specified" do
      Application.put_env(:drops, :logger,
        handler: :console,
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler defaults to string formatter
      {:ok, config} = :logger.get_handler_config(:drops_debug_handler)
      # The formatter config should be normalized to String formatter
      assert {Drops.Logger.Formatter.String, %Drops.Logger.Formatter.Config{}} =
               config.formatter
    end
  end

  describe "built-in handler integration" do
    test "uses logger_std_h for console handler" do
      Application.put_env(:drops, :logger,
        handler: :console,
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler uses built-in logger_std_h module
      {:ok, config} = :logger.get_handler_config(:drops_debug_handler)
      assert config.module == :logger_std_h
      assert config.config.type == :standard_io
    end

    test "uses logger_std_h for file handler" do
      Application.put_env(:drops, :logger,
        handler: :file,
        file: "test/tmp/builtin_test.log",
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler uses built-in logger_std_h module
      {:ok, config} = :logger.get_handler_config(:drops_debug_handler)
      assert config.module == :logger_std_h
      assert String.ends_with?(to_string(config.config.file), "test/tmp/builtin_test.log")

      # Clean up test file
      File.rm_rf!("test/tmp")
    end

    test "uses custom TestHandler for memory handler" do
      Application.put_env(:drops, :logger,
        handler: :memory,
        level: :debug
      )

      assert :ok = DropsLogger.add_handler()

      # Verify handler uses custom DebugHandler module
      {:ok, config} = :logger.get_handler_config(:drops_debug_handler)
      assert config.module == Drops.Logger.TestHandler
    end
  end
end
