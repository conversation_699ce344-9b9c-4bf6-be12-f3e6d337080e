defmodule Drops.Logger.Formatter.Structured do
  @moduledoc """
  Structured (JSON) formatter for Drops operations debug logging.

  This formatter produces JSON output with structured metadata.
  Only processes logs that have operation-related metadata.

  ## Format

  The output is a JSON object with the following structure:

      {
        "level": "debug",
        "message": "Operation message",
        "metadata": {
          "operation": "OperationName",
          "step": "step_name"
        }
      }

  Note: Timestamps are not generated by formatters as all log entries
  already have timestamps available.

  ## JSON Backend

  The formatter uses a JSON backend in the following priority:
  1. Built-in JSON from Elixir (if available)
  2. Jason library (if available)
  3. Raises an error if neither is available

  ## Configuration

  The formatter accepts a `Drops.Logger.Formatter.Config` struct with options:

  * `colorize` - Not used for structured output (ignored)
  * `add_newline` - Whether to add newlines to formatted output (default: true)
  """

  alias Drops.Logger.Formatter.Config

  @doc """
  Formats a log event as structured JSON.

  ## Parameters

  * `log_event` - The log event map from the logger
  * `config` - Formatter configuration struct

  ## Returns

  A JSON string if the log event has operation metadata, empty string otherwise.
  """
  @spec format(map(), Config.t()) :: String.t()
  def format(log_event, %Config{} = config) do
    if is_operation_log?(log_event) do
      level = Map.get(log_event, :level, :debug)
      message = extract_message(log_event)

      # Use configured metadata fields, defaulting to [:operation, :step]
      app_config = Application.get_env(:drops, :logger, [])
      metadata_fields = Keyword.get(app_config, :metadata, [:operation, :step])
      metadata = extract_metadata_map(log_event, metadata_fields)

      # Use timestamp from log event if available, otherwise generate current timestamp
      timestamp =
        case Map.get(log_event, :time) do
          nil -> System.system_time(:millisecond)
          time_microseconds -> div(time_microseconds, 1000)
        end

      json_data = %{
        level: level,
        message: message,
        metadata: metadata,
        timestamp: timestamp
      }

      result = encode!(json_data)

      if config.add_newline do
        result <> "\n"
      else
        result
      end
    else
      # Return empty string for non-operation logs to filter them out
      ""
    end
  end

  @doc """
  Encodes data to JSON using the best available JSON backend.

  Uses built-in JSON from Elixir if available, falls back to Jason,
  and raises an error if neither is available.

  ## Parameters

  * `data` - The data to encode as JSON

  ## Returns

  A JSON string representation of the data.

  ## Raises

  * `RuntimeError` - If no JSON backend is available
  """
  @spec encode!(term()) :: String.t()
  def encode!(data) do
    encode!(data, backend())
  end

  @spec encode!(term(), module()) :: String.t()
  def encode!(data, backend) do
    backend.encode!(data)
  end

  if Code.ensure_loaded?(Jason) do
    defp backend, do: Jason
  else
    defp backend, do: JSON
  end

  defp is_operation_log?(log_event) do
    meta = Map.get(log_event, :meta, %{})
    Map.has_key?(meta, :operation)
  end

  defp extract_message(log_event) do
    case Map.get(log_event, :msg) do
      {:string, message} when is_binary(message) ->
        message

      message when is_binary(message) ->
        message

      message when is_list(message) ->
        IO.iodata_to_binary(message)

      {format, args} when is_binary(format) and is_list(args) ->
        try do
          :io_lib.format(format, args) |> IO.iodata_to_binary()
        rescue
          _ -> inspect({format, args})
        end

      other ->
        inspect(other)
    end
  end

  defp extract_metadata_map(log_event, metadata_fields) do
    meta = Map.get(log_event, :meta, %{})

    metadata_fields
    |> Enum.filter(&Map.has_key?(meta, &1))
    |> Enum.into(%{}, fn field ->
      value = Map.get(meta, field)
      {field, value}
    end)
  end
end
